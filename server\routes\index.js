import express from "express";
import { improveRoute } from "./improveRoute.js";
import convertRoutes from "./convertRoute.js";
import { validateImproveRequest } from "../middleware/validation.js";
import { requireAuth } from "../middleware/auth.js";
import exportRoutes from "./exportRoute.js";

const router = express.Router();

router.post("/improve", requireAuth, validateImproveRequest, improveRoute);
router.use("/convert", convertRoutes);
router.use("/export", exportRoutes);

export default router;
