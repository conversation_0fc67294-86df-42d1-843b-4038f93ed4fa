import dotenv from "dotenv";

// Load environment variables
dotenv.config();

// Required environment variables
const requiredEnvVars = [
  "OPENAI_API_KEY",
  "PORT",
  "AUTH_USERNAME",
  "AUTH_PASSWORD",
  "SESSION_SECRET",
];

// Validate environment variables
export const validateEnv = () => {
  const missingEnvVars = requiredEnvVars.filter(
    (envVar) => !process.env[envVar]
  );
  if (missingEnvVars.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missingEnvVars.join(", ")}`
    );
  }
};

export const PORT = process.env.PORT || 3001;

// Authentication configuration
export const AUTH_USERNAME = process.env.AUTH_USERNAME;
export const AUTH_PASSWORD = process.env.AUTH_PASSWORD;
export const SESSION_SECRET = process.env.SESSION_SECRET;
