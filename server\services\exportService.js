import fs from "fs";
import path from "path";
import { jsPDF } from "jspdf";
import createCsvWriter from "csv-writer";

/**
 * Generate PDF from gig data
 * @param {Object} gigData - The gig data to export
 * @returns {Promise<Buffer>} - PDF buffer
 */
export const generatePDF = async (gigData) => {
  try {
    // Create new PDF document
    const doc = new jsPDF({
      orientation: "portrait",
      unit: "mm",
      format: "a4",
    });

    // Set up document properties
    doc.setProperties({
      title: `Gig Description - ${gigData.title || "Untitled"}`,
      subject: "Gig Description",
      author: "Hirer Advisor v2",
      creator: "Hirer Advisor v2",
    });

    // Add content to PDF
    addContentToPDF(doc, gigData);

    // Return PDF as buffer
    const pdfBuffer = Buffer.from(doc.output("arraybuffer"));
    return pdfBuffer;
  } catch (error) {
    console.error("PDF generation error:", error);
    throw new Error("Failed to generate PDF");
  }
};

/**
 * Generate JSON export
 * @param {Object} gigData - The gig data to export
 * @returns {string} - JSON string
 */
export const generateJSON = (gigData) => {
  try {
    const exportData = {
      exportedAt: new Date().toISOString(),
      version: "1.0",
      gig: gigData,
    };

    return JSON.stringify(exportData, null, 2);
  } catch (error) {
    console.error("JSON generation error:", error);
    throw new Error("Failed to generate JSON");
  }
};

/**
 * Generate CSV export
 * @param {Object} gigData - The gig data to export
 * @returns {Promise<string>} - CSV content
 */
export const generateCSV = async (gigData) => {
  try {
    // Create temporary file path
    const tempDir = "temp";
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    const tempFilePath = path.join(tempDir, `gig-export-${Date.now()}.csv`);

    // Configure CSV writer
    const csvWriter = createCsvWriter.createObjectCsvWriter({
      path: tempFilePath,
      header: [
        { id: "field", title: "Field" },
        { id: "content", title: "Content" },
      ],
    });

    // Convert gig data to CSV format
    const csvData = Object.entries(gigData).map(([key, value]) => ({
      field: formatFieldName(key),
      content: value || "",
    }));

    // Write CSV file
    await csvWriter.writeRecords(csvData);

    // Read the file content
    const csvContent = fs.readFileSync(tempFilePath, "utf8");

    // Clean up temporary file
    fs.unlinkSync(tempFilePath);

    return csvContent;
  } catch (error) {
    console.error("CSV generation error:", error);
    throw new Error("Failed to generate CSV");
  }
};

/**
 * Add content to PDF document
 * @param {jsPDF} doc - The PDF document
 * @param {Object} gigData - The gig data
 */
const addContentToPDF = (doc, gigData) => {
  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();
  const margin = 20;
  const contentWidth = pageWidth - margin * 2;
  let yPosition = margin;

  // Header
  doc.setFontSize(20);
  doc.setFont("helvetica", "bold");
  const title = gigData.title || "Gig Description";
  yPosition += 10;
  doc.text(title, pageWidth / 2, yPosition, { align: "center" });

  // Export date
  yPosition += 10;
  doc.setFontSize(10);
  doc.setFont("helvetica", "normal");
  const exportDate = `Exported on ${new Date().toLocaleDateString()}`;
  doc.text(exportDate, pageWidth / 2, yPosition, { align: "center" });

  // Line under header
  yPosition += 5;
  doc.setLineWidth(0.5);
  doc.line(margin, yPosition, pageWidth - margin, yPosition);
  yPosition += 15;

  // Sections
  const sections = [
    { title: "Project Summary", content: gigData.summary },
    { title: "Company Background", content: gigData.companyBackground },
    { title: "Deliverables", content: gigData.deliverables },
    { title: "Required Skills and Qualifications", content: gigData.skills },
    { title: "Budget", content: gigData.budget },
    { title: "Timeline", content: gigData.timeline },
    { title: "Communication Preferences", content: gigData.communication },
    {
      title: "Ownership and Intellectual Property",
      content: gigData.ownership,
    },
    {
      title: "Confidentiality and NDA Requirements",
      content: gigData.confidentiality,
    },
    { title: "Additional Notes", content: gigData.notes },
  ];

  sections.forEach((section) => {
    yPosition = addSectionToPDF(
      doc,
      section.title,
      section.content,
      yPosition,
      margin,
      contentWidth,
      pageHeight
    );
  });

  // Footer
  const footerY = pageHeight - 15;
  doc.setFontSize(8);
  doc.setFont("helvetica", "italic");
  doc.text("Generated by Hirer Advisor v2", pageWidth / 2, footerY, {
    align: "center",
  });
};

/**
 * Add a section to the PDF
 * @param {jsPDF} doc - The PDF document
 * @param {string} title - Section title
 * @param {string} content - Section content
 * @param {number} yPosition - Current Y position
 * @param {number} margin - Page margin
 * @param {number} contentWidth - Content width
 * @param {number} pageHeight - Page height
 * @returns {number} - New Y position
 */
const addSectionToPDF = (
  doc,
  title,
  content,
  yPosition,
  margin,
  contentWidth,
  pageHeight
) => {
  // Check if we need a new page
  if (yPosition > pageHeight - 60) {
    doc.addPage();
    yPosition = margin;
  }

  // Section title
  doc.setFontSize(14);
  doc.setFont("helvetica", "bold");
  doc.text(title, margin, yPosition);
  yPosition += 8;

  // Section content
  doc.setFontSize(10);
  doc.setFont("helvetica", "normal");

  const sectionContent =
    content && content.trim().length > 0 ? content : "Not specified";

  // Split text to fit within page width
  const lines = doc.splitTextToSize(sectionContent, contentWidth);

  // Check if content fits on current page
  const contentHeight = lines.length * 4;
  if (yPosition + contentHeight > pageHeight - 30) {
    doc.addPage();
    yPosition = margin;

    // Re-add section title on new page
    doc.setFontSize(14);
    doc.setFont("helvetica", "bold");
    doc.text(title, margin, yPosition);
    yPosition += 8;
    doc.setFontSize(10);
    doc.setFont("helvetica", "normal");
  }

  // Add content lines
  lines.forEach((line) => {
    doc.text(line, margin, yPosition);
    yPosition += 4;
  });

  yPosition += 10; // Space between sections
  return yPosition;
};

/**
 * Format field names for display
 * @param {string} fieldName - Raw field name
 * @returns {string} - Formatted field name
 */
const formatFieldName = (fieldName) => {
  const fieldMap = {
    title: "Gig Title",
    summary: "Project Summary",
    companyBackground: "Company Background",
    deliverables: "Deliverables",
    skills: "Required Skills and Qualifications",
    budget: "Budget",
    timeline: "Timeline",
    communication: "Communication Preferences",
    ownership: "Ownership and Intellectual Property",
    confidentiality: "Confidentiality and NDA Requirements",
    notes: "Additional Notes",
  };

  return fieldMap[fieldName] || fieldName;
};
