import express from 'express';
import { generatePDF, generateJSON, generateCSV } from '../services/exportService.js';
import { asyncHandler } from '../middleware/errorHandler.js';
import { requireAuth } from '../middleware/auth.js';

const router = express.Router();

/**
 * Validate gig data for export
 * @param {Object} gigData - The gig data to validate
 * @returns {boolean} - Whether the data is valid
 */
const validateGigData = (gigData) => {
  if (!gigData || typeof gigData !== 'object') {
    return false;
  }
  
  // Check if at least the title or summary exists
  return gigData.title || gigData.summary;
};

/**
 * Export gig as PDF
 */
router.post('/pdf', 
  requireAuth,
  asyncHandler(async (req, res) => {
    const { gigData } = req.body;
    
    if (!validateGigData(gigData)) {
      return res.status(400).json({
        error: 'Invalid gig data',
        message: 'Please provide valid gig data to export'
      });
    }
    
    try {
      const pdfBuffer = await generatePDF(gigData);
      
      // Set headers for PDF download
      const filename = `gig-description-${Date.now()}.pdf`;
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Content-Length', pdfBuffer.length);
      
      // Send the PDF
      res.send(pdfBuffer);
      
    } catch (error) {
      console.error('PDF export error:', error);
      res.status(500).json({
        error: 'Export failed',
        message: 'Failed to generate PDF. Please try again.'
      });
    }
  })
);

/**
 * Export gig as JSON
 */
router.post('/json',
  requireAuth,
  asyncHandler(async (req, res) => {
    const { gigData } = req.body;
    
    if (!validateGigData(gigData)) {
      return res.status(400).json({
        error: 'Invalid gig data',
        message: 'Please provide valid gig data to export'
      });
    }
    
    try {
      const jsonContent = generateJSON(gigData);
      
      // Set headers for JSON download
      const filename = `gig-description-${Date.now()}.json`;
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      
      // Send the JSON
      res.send(jsonContent);
      
    } catch (error) {
      console.error('JSON export error:', error);
      res.status(500).json({
        error: 'Export failed',
        message: 'Failed to generate JSON. Please try again.'
      });
    }
  })
);

/**
 * Export gig as CSV
 */
router.post('/csv',
  requireAuth,
  asyncHandler(async (req, res) => {
    const { gigData } = req.body;
    
    if (!validateGigData(gigData)) {
      return res.status(400).json({
        error: 'Invalid gig data',
        message: 'Please provide valid gig data to export'
      });
    }
    
    try {
      const csvContent = await generateCSV(gigData);
      
      // Set headers for CSV download
      const filename = `gig-description-${Date.now()}.csv`;
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      
      // Send the CSV
      res.send(csvContent);
      
    } catch (error) {
      console.error('CSV export error:', error);
      res.status(500).json({
        error: 'Export failed',
        message: 'Failed to generate CSV. Please try again.'
      });
    }
  })
);

/**
 * Get available export formats
 */
router.get('/formats',
  requireAuth,
  (req, res) => {
    res.json({
      formats: [
        {
          type: 'pdf',
          name: 'PDF Document',
          description: 'Professional formatted document',
          mimeType: 'application/pdf'
        },
        {
          type: 'json',
          name: 'JSON Data',
          description: 'Structured data format',
          mimeType: 'application/json'
        },
        {
          type: 'csv',
          name: 'CSV Spreadsheet',
          description: 'Comma-separated values for spreadsheets',
          mimeType: 'text/csv'
        }
      ]
    });
  }
);

export default router;
