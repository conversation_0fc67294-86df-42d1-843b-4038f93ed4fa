import { AUTH_USERNAME, AUTH_PASSWORD } from '../config/env.js';

/**
 * Authentication middleware to protect routes
 * Checks if user is authenticated via session
 */
export const requireAuth = (req, res, next) => {
  if (req.session && req.session.authenticated) {
    return next();
  } else {
    return res.status(401).json({ 
      error: 'Authentication required',
      message: 'Please log in to access this resource'
    });
  }
};

/**
 * <PERSON><PERSON> endpoint handler
 * Validates credentials and creates session
 */
export const login = (req, res) => {
  const { username, password } = req.body;

  // Validate request body
  if (!username || !password) {
    return res.status(400).json({
      error: 'Missing credentials',
      message: 'Username and password are required'
    });
  }

  // Check credentials against environment variables
  // TODO: In production, use proper password hashing (bcrypt) and database storage
  if (username === AUTH_USERNAME && password === AUTH_PASSWORD) {
    // Set session
    req.session.authenticated = true;
    req.session.username = username;
    
    return res.json({
      success: true,
      message: 'Login successful',
      user: { username }
    });
  } else {
    return res.status(401).json({
      error: 'Invalid credentials',
      message: 'Username or password is incorrect'
    });
  }
};

/**
 * Logout endpoint handler
 * Destroys session
 */
export const logout = (req, res) => {
  req.session.destroy((err) => {
    if (err) {
      return res.status(500).json({
        error: 'Logout failed',
        message: 'Could not log out, please try again'
      });
    }
    
    res.clearCookie('connect.sid'); // Clear session cookie
    return res.json({
      success: true,
      message: 'Logout successful'
    });
  });
};

/**
 * Check authentication status
 * Returns current authentication state
 */
export const checkAuth = (req, res) => {
  if (req.session && req.session.authenticated) {
    return res.json({
      authenticated: true,
      user: { username: req.session.username }
    });
  } else {
    return res.json({
      authenticated: false
    });
  }
};
