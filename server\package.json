{"name": "hirer-advisor-server", "version": "1.0.0", "description": "Server for Hirer Advisor application with OpenAI integration", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"cors": "^2.8.5", "csv-writer": "^1.6.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-session": "^1.17.3", "jspdf": "^2.5.1", "mammoth": "^1.6.0", "multer": "^1.4.5-lts.1", "openai": "^4.28.0", "pdfjs-dist": "^4.0.379"}, "devDependencies": {"nodemon": "^3.0.1"}}