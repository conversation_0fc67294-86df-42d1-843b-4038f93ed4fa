export interface ExportFormat {
  type: 'pdf' | 'json' | 'csv';
  name: string;
  description: string;
  mimeType: string;
}

export interface ExportResponse {
  success: boolean;
  error?: string;
  message?: string;
}

export interface ExportState {
  isExporting: boolean;
  error: string | null;
  success: boolean;
  format: string | null;
}

export const EXPORT_FORMATS: ExportFormat[] = [
  {
    type: 'pdf',
    name: 'PDF Document',
    description: 'Professional formatted document',
    mimeType: 'application/pdf'
  },
  {
    type: 'json',
    name: 'JSON Data',
    description: 'Structured data format',
    mimeType: 'application/json'
  },
  {
    type: 'csv',
    name: 'CSV Spreadsheet',
    description: 'Comma-separated values for spreadsheets',
    mimeType: 'text/csv'
  }
];
