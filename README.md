# Hirer Advisor

Hirer Advisor is a web application that helps users create professional gig descriptions for freelance projects. It uses AI to provide suggestions and improvements for different sections of the gig description.

## Features

- Section-by-section guided journey to create a complete gig description
- AI-powered interface for getting suggestions
- Simple authentication system with login/logout functionality
- Session-based authentication with protected routes
- User-friendly login interface with error handling

## Tech Stack

- **Frontend**: React, TypeScript, Tailwind CSS, Vite
- **Backend**: Node.js, Express
- **Authentication**: Express-session, HTTP-only cookies
- **AI**: OpenAI API (GPT-4o-mini)

## Project Structure

- `/client` - Frontend React application
- `/server` - Backend Express server

## Setup

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn
- OpenAI API key

### Installation

1. Clone the repository

2. Install dependencies for the frontend:

   ```
   cd client
   npm install
   ```

3. Install dependencies for the backend:

   ```
   cd server
   npm install
   ```

4. Configure environment variables:

   - Create a `.env` file in the server directory
   - Add the following configuration to the `.env` file:

     ```
     PORT=3001
     OPENAI_API_KEY=your_openai_api_key_here

     # Authentication Configuration
     AUTH_USERNAME=username
     AUTH_PASSWORD=password
     SESSION_SECRET=your_random_session_secret_key
     ```

## Running the Application

### Development Mode

Start the frontend:

```
cd client
npm run dev
```

Start the backend:

```
cd server
npm run dev
```

- Frontend will be available at: http://localhost:5173
- Backend will be available at: http://localhost:3001

### Production Mode

Start the frontend:

```
cd client
npm run build
```

Start the backend:

```
cd server
npm start
```

### Authentication

For production deployment, you MUST:

1. **Change the default credentials** in your environment variables:

   ```env
   AUTH_USERNAME=your_production_username
   AUTH_PASSWORD=your_secure_production_password
   SESSION_SECRET=your_random_session_secret_key
   ```

2. **Use strong passwords** - The production password should be:

   - At least 12 characters long
   - Include uppercase, lowercase, numbers, and special characters
   - Not be a dictionary word or common password

3. **Generate a secure session secret** - Use a random string generator:
   ```bash
   # Example using Node.js
   node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
   ```

## Usage

1. **Login:** Navigate to the application and log in with your credentials
2. Fill out the gig description form with your project details
3. Use the AI chat interface to get help with the overall gig description
4. Use the AI suggest button to get suggestions for specific sections
5. **Logout:** Use the logout button in the header when finished
